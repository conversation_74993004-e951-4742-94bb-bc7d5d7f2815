<template>
  <div class="dashboard-container">
    <!-- Header com logo e boas-vindas -->
    <div class="dashboard-header">
      <div class="welcome-section">
        <img :src="LumiBlueLogo" class="dashboard-logo" alt="Lumi Vision" />
        <div class="welcome-text">
          <h1 class="welcome-title">Bem-vindo ao Lumi Vision</h1>
          <p class="welcome-subtitle">Escolha uma opção para começar</p>
        </div>
      </div>
    </div>

    <!-- Grid de atalhos principais -->
    <div class="shortcuts-grid">
      <!-- Agenda -->
      <div class="shortcut-card main-shortcut" @click="navigateTo('agenda')">
        <div class="shortcut-icon">
          <i class="mdi mdi-calendar-month"></i>
        </div>
        <div class="shortcut-content">
          <h3 class="shortcut-title">Agenda</h3>
          <p class="shortcut-description">Gerencie consultas e compromissos</p>
        </div>
      </div>

      <!-- Pacientes -->
      <div class="shortcut-card main-shortcut" @click="navigateTo('pacientes')">
        <div class="shortcut-icon">
          <i class="mdi mdi-account-details"></i>
        </div>
        <div class="shortcut-content">
          <h3 class="shortcut-title">Pacientes</h3>
          <p class="shortcut-description">Cadastro e histórico de pacientes</p>
        </div>
      </div>

      <!-- Mentorias -->
      <div class="shortcut-card main-shortcut" @click="navigateTo('mentorias')">
        <div class="shortcut-icon">
          <i class="mdi mdi-school"></i>
        </div>
        <div class="shortcut-content">
          <h3 class="shortcut-title">Mentorias</h3>
          <p class="shortcut-description">Acompanhamento e orientações</p>
        </div>
      </div>

      <!-- Financeiro -->
      <div class="shortcut-card main-shortcut" @click="navigateTo('financeiro')">
        <div class="shortcut-icon">
          <i class="mdi mdi-currency-usd"></i>
        </div>
        <div class="shortcut-content">
          <h3 class="shortcut-title">Financeiro</h3>
          <p class="shortcut-description">Controle financeiro e relatórios</p>
        </div>
      </div>

      <!-- Configurações -->
      <div class="shortcut-card config-shortcut" @click="navigateTo('configuracoes')">
        <div class="shortcut-icon small">
          <i class="mdi mdi-cog"></i>
        </div>
        <div class="shortcut-content">
          <h4 class="shortcut-title small">Configurações</h4>
        </div>
      </div>
    </div>

    <!-- Footer com informações -->
    <div class="dashboard-footer">
      <p class="footer-text">© {{ new Date().getFullYear() }} Lumi Vision - Sistema de Gestão Ortodôntica</p>
    </div>
  </div>
</template>
<script>
import LumiBlueLogo from "@/assets/img/lumi/lumi-vision-logo-170.png";

export default {
  name: "Inicio",
  data() {
    return {
      LumiBlueLogo,
    };
  },
  methods: {
    navigateTo(route) {
      this.$router.push(`/${route}`);
    },
  },
};
</script>

<style scoped>
.dashboard-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fbff 0%, #e8f4fd 100%);
  padding: 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

/* Header */
.dashboard-header {
  text-align: center;
  margin-bottom: 3rem;
}

.welcome-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
}

.dashboard-logo {
  width: 120px;
  height: auto;
  filter: drop-shadow(0 4px 12px rgba(44, 130, 201, 0.2));
  transition: transform 0.3s ease;
}

.dashboard-logo:hover {
  transform: scale(1.05);
}

.welcome-text {
  color: #2C82C9;
}

.welcome-title {
  font-size: 2.5rem;
  font-weight: 600;
  margin: 0;
  background: linear-gradient(135deg, #2C82C9, #4A9FE7);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.welcome-subtitle {
  font-size: 1.1rem;
  margin: 0.5rem 0 0 0;
  opacity: 0.8;
  font-weight: 400;
}

/* Grid de atalhos */
.shortcuts-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, 1fr) auto;
  gap: 1.5rem;
  max-width: 600px;
  width: 100%;
  margin-bottom: 2rem;
}

/* Configurações ocupa apenas uma célula na terceira linha */
.config-shortcut {
  grid-column: 1 / 2;
  grid-row: 3;
}

/* Cards de atalho */
.shortcut-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fbff 100%);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(44, 130, 201, 0.1);
  border: 2px solid rgba(44, 130, 201, 0.1);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.shortcut-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #2C82C9, #4A9FE7, #73A9D1);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.shortcut-card:hover::before {
  transform: scaleX(1);
}

.shortcut-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(44, 130, 201, 0.2);
  border-color: rgba(44, 130, 201, 0.3);
}

.shortcut-card:active {
  transform: translateY(-4px);
}

/* Ícones */
.shortcut-icon {
  width: 80px;
  height: 80px;
  border-radius: 20px;
  background: linear-gradient(135deg, #2C82C9, #4A9FE7);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  transition: all 0.3s ease;
}

.shortcut-icon.small {
  width: 60px;
  height: 60px;
  margin-bottom: 1rem;
}

.shortcut-icon i {
  font-size: 2.5rem;
  color: white;
}

.shortcut-icon.small i {
  font-size: 2rem;
}

.shortcut-card:hover .shortcut-icon {
  transform: scale(1.1);
  box-shadow: 0 8px 24px rgba(44, 130, 201, 0.3);
}

/* Conteúdo dos cards */
.shortcut-content {
  flex: 1;
}

.shortcut-title {
  font-size: 1.4rem;
  font-weight: 600;
  color: #2C82C9;
  margin: 0 0 0.5rem 0;
}

.shortcut-title.small {
  font-size: 1.1rem;
  margin: 0;
}

.shortcut-description {
  font-size: 0.95rem;
  color: #73848D;
  margin: 0;
  line-height: 1.4;
}

/* Footer */
.dashboard-footer {
  text-align: center;
  margin-top: auto;
}

.footer-text {
  font-size: 0.9rem;
  color: #73848D;
  margin: 0;
  opacity: 0.8;
}

/* Responsividade */
@media (max-width: 768px) {
  .dashboard-container {
    padding: 1.5rem;
  }

  .dashboard-logo {
    width: 90px;
  }

  .welcome-title {
    font-size: 1.8rem;
  }

  .welcome-subtitle {
    font-size: 1rem;
  }

  .shortcuts-grid {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(2, 1fr) auto;
    gap: 1rem;
    max-width: 500px;
  }

  .config-shortcut {
    grid-column: 1 / 3;
    grid-row: 3;
    max-width: 240px;
    justify-self: center;
  }

  .shortcut-card {
    padding: 1.25rem;
  }

  .shortcut-icon {
    width: 60px;
    height: 60px;
    margin-bottom: 1rem;
  }

  .shortcut-icon.small {
    width: 50px;
    height: 50px;
  }

  .shortcut-icon i {
    font-size: 1.8rem;
  }

  .shortcut-icon.small i {
    font-size: 1.5rem;
  }

  .shortcut-title {
    font-size: 1.1rem;
  }

  .shortcut-title.small {
    font-size: 1rem;
  }

  .shortcut-description {
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .dashboard-container {
    padding: 1rem;
  }

  .dashboard-logo {
    width: 80px;
  }

  .welcome-title {
    font-size: 1.6rem;
  }

  .welcome-subtitle {
    font-size: 0.95rem;
  }

  .shortcuts-grid {
    max-width: 400px;
    gap: 0.8rem;
  }

  .shortcut-card {
    padding: 1rem;
  }

  .shortcut-icon {
    width: 55px;
    height: 55px;
    margin-bottom: 0.8rem;
  }

  .shortcut-icon.small {
    width: 45px;
    height: 45px;
  }

  .shortcut-icon i {
    font-size: 1.6rem;
  }

  .shortcut-icon.small i {
    font-size: 1.3rem;
  }

  .shortcut-title {
    font-size: 1rem;
  }

  .shortcut-title.small {
    font-size: 0.9rem;
  }

  .shortcut-description {
    font-size: 0.8rem;
  }
}
</style>
